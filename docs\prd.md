# Product Requirements Document (PRD): Integrated Business Management Ecosystem (V2.0 - Full Version)

## 1. Goals and Background Context

### Goals
- To provide a single, integrated platform for Small and Medium-sized Enterprises (SMEs) to replace their current fragmented software tools.
- To significantly reduce manual workload and human error in operations through automated data synchronization (Lazada/POS → WMS → Accounting).
- To build a powerful and self-contained ecosystem (including Accounting, POS, WMS modules) as the first version of the project for market entry.
- To lay a solid technical and product foundation for future platformization (open API, introduction of more modules) and regional expansion.

### Background Context
Currently, many SME retailers face the dilemma of "digital silos." They rely on multiple independent software to handle accounting, sales (POS), warehouse (WMS), and online sales (Lazada, etc.), preventing the free flow of data. This consumes significant manpower for manual data migration and reconciliation and severely impacts the accuracy of inventory management and business decisions due to information lag. This project aims to completely solve this pain point by providing a seamlessly integrated, all-in-one solution, helping business owners gain real-time control of their entire business and improve operational efficiency.

### Change Log
| Date       | Version | Description              | Author      |
| :--------- | :------ | :----------------------- | :---------- |
| 2025-08-04 | 1.0     | Initial Draft Creation   | John (PM)   |
| 2025-08-04 | 2.0     | Switched from MVP to Full Project scope | John (PM)   |


## 2. Requirements

### Functional Requirements (FR)
- **FR1: User & Permission Management**: Users can register via email/password. The system supports role-based access control (e.g., Admin, Accountant, POS Staff).
- **FR2: Lazada Integration**: Users can securely connect their Lazada store via OAuth 2.0. The system can sync orders and products.
- **FR3: WMS Module**: The system maintains a central product master and inventory ledger, with support for manual adjustments and audit logs.
- **FR4: POS Module**: A web-based POS interface is provided for in-store sales, which deducts from the central inventory.
- **FR5: Accounting Module**: The system includes a customizable Chart of Accounts, automatically generates journal entries from sales, and can produce basic financial reports.

### Non-Functional Requirements (NFR)
- **NFR1: Performance**: API responses for typical user actions should be under 500ms. Order sync latency should not exceed 5 minutes.
- **NFR2: Security**: All sensitive user data must be encrypted at rest and in transit. The system must comply with local data protection laws (e.g., PDPA).
- **NFR3: Usability**: The initial setup for an e-commerce store should be completable by a non-technical user in under 15 minutes.
- **NFR4: Reliability**: Target uptime of 99.5%; data sync must have robust error handling, reconciliation, and retry mechanisms. Concurrency control must be in place to prevent overselling.

## 3. User Interface Design Goals

- **Overall UX Vision**: To create an intuitive, efficient, and reliable business management tool. The UI should strive for simplicity, simplifying complex operational workflows to be accessible for non-technical business owners.
- **Key Interaction Paradigms**: The application will be a unified, modular admin dashboard. The design will be Dashboard-Driven for overviews and Task-Oriented for specific high-frequency workflows like POS sales.
- **Target Devices**: Web Responsive. The admin backend is desktop-first; the POS interface must be optimized for tablets.

## 4. Technical Assumptions

- **Repository Structure**: A Monorepo will be used to manage the entire codebase.
- **Service Architecture**: The project will start as a Well-Structured Monolith (Event-Driven), designed for a future evolution towards a Microservices architecture.
- **Testing Requirements**: The testing strategy will focus on Unit Testing and Integration Testing.
- **Additional Assumptions**: The entire system will be designed based on container technology (Docker). The tech stack is Go for the backend, React (Next.js) for the frontend, and PostgreSQL for the database.

## 5. Epic List

- **Epic 1**: Project Foundation & Core Data
- **Epic 2**: Core WMS - Inventory Operations
- **Epic 3**: Core POS - Offline Sales Loop
- **Epic 4**: E-commerce Integration - Lazada Channel
- **Epic 5**: Core Accounting Engine
- **Epic 6**: Business Intelligence - Reports & Dashboards

## 6. Epic Details

### Epic 1: Project Foundation & Core Data
**Epic Goal**: To build a secure, scalable, multi-tenant "foundation" for the entire SaaS ecosystem. We will complete all necessary project setup, configure the user and permissions system, implement multi-tenant data isolation, establish subscription management, and create the management function for product master data.

---
#### Story 1.1: Project Initialization & CI/CD Configuration
- **As a** Development Lead, **I want** an initialized Monorepo repository with basic frontend and backend configurations, along with an automated CI/CD pipeline, **so that** team members can develop and deploy efficiently and consistently in a unified, automated environment.
- **Acceptance Criteria**:
    1. A Monorepo structure is successfully created.
    2. The repository contains basic application skeletons for the frontend (React) and backend (Go).
    3. A simple "health check" API endpoint can be successfully built and deployed to a development environment.
    4. A CI pipeline is configured to automatically run builds and tests on code commits.
    5. A CD pipeline is configured to automatically deploy the application to a development environment.

---
#### Story 1.2: Implement User Authentication & Basic Role Management
- **As a** System Administrator, **I want** to be able to manage user accounts and their role-based permissions, **so that** I can securely control access to different parts of the system.
- **Acceptance Criteria**:
    1. New users can register with an email and password.
    2. Registered users can successfully log in to the system and receive a JWT.
    3. API endpoints can be protected, requiring a valid JWT for access.
    4. The system includes at least three user roles: Administrator, Accountant, POS Staff.
    5. An administrator can assign and modify roles for other users.

---
#### Story 1.3: Design and Implement Multi-tenant Data Isolation
- **As a** System Architect, **I want** all business data in the system to be strictly isolated by tenant (company), **so that** we can absolutely guarantee that no company's data can be accessed by another.
- **Acceptance Criteria**:
    1. The system defines a "Tenant," where each registered company is an independent tenant.
    2. All core business data tables include a `tenant_id` column.
    3. The system automatically and mandatorily filters data based on the current user's `tenant_id` at the database level.
    4. Automated tests must prove that a user from Tenant A cannot access data from Tenant B.

---
#### Story 1.4: Implement Subscription Plan and Feature Entitlement
- **As a** Product Operator, **I want** a management system to define different subscription plans and control feature access, **so that** we can flexibly package and sell our product features.
- **Acceptance Criteria**:
    1. A back-end interface is provided for creating and editing subscription plans (e.g., Basic, Pro).
    2. Different feature permissions can be associated with each plan.
    3. An administrator can assign a subscription plan to each tenant.
    4. A reliable permission-checking mechanism is implemented in the code.
    5. Automated tests must prove that a "Basic" plan user cannot access a "Pro" feature.

---
#### Story 1.5: Create and Manage Product Master Data
- **As a** Business Manager, **I want** to be able to create, view, update, and delete product information in a centralized catalog, **so that** I can maintain a single source of truth for all items.
- **Acceptance Criteria**:
    1. A UI is provided to list all products.
    2. Authorized users can create a new product with at least SKU, name, cost price, and selling price.
    3. Users can edit the details of an existing product.
    4. The system validates that each SKU is unique within the same tenant.
    5. All product data must be associated with the current user's `tenant_id`.

### Epic 2: Core WMS - Inventory Operations
**Epic Goal**: To transform static product information into dynamic, operational inventory assets. We will build the core functionality of the WMS, including a real-time inventory ledger and a complete set of inbound, outbound, and stock-take adjustment processes.

---
#### Story 2.1: Establish Product Inventory Ledger
- **As a** Warehouse Manager, **I want** every product in the system to have an associated real-time stock quantity, **so that** I always know the exact inventory level for each item.
- **Acceptance Criteria**:
    1. When a new product is created, an inventory record is automatically created for it with an initial quantity of 0.
    2. A new UI page, "Inventory Management," is created to list all products and their current stock levels.
    3. The stock level is displayed as a non-editable field in the main list view.

---
#### Story 2.2: Implement Manual Stock Adjustment Functionality
- **As a** Warehouse Manager, **I need** to be able to manually adjust the stock levels for any product, **so that** I can perform initial stock-in, correct discrepancies, or record stock loss.
- **Acceptance Criteria**:
    1. An "Adjust Stock" function is available for each product on the "Inventory Management" page.
    2. The function allows the user to select an adjustment type (e.g., "Purchase Stock-in," "Correction," "Damage/Loss").
    3. The user can enter a positive or negative quantity for the adjustment.
    4. The user must provide a brief reason or reference note.
    5. Upon submission, the product's stock level is updated accordingly.

---
#### Story 2.3: Record an Audit Log for all Inventory Movements
- **As a** Business Owner, **I want** every change to a product's stock level to be recorded in a detailed log, **so that** I can track all inventory movements and investigate discrepancies.
- **Acceptance Criteria**:
    1. Every stock adjustment must generate an immutable log entry.
    2. The log entry must record: timestamp, product SKU, quantity change, final quantity, adjustment type, the user who made the change, and the reference note.
    3. A new UI page, "Inventory History," allows a user to select a product and view its complete inventory movement history.

### Epic 3: Core POS - Offline Sales Loop
**Epic Goal**: To empower the user's physical store operations by developing a functional and easy-to-use Web POS terminal that links directly with the real-time inventory system.

---
#### Story 3.1: Build POS Sales Interface and Cart Functionality
- **As a** POS Staff, **I want** a clean and responsive interface to search for products and add them to a customer's cart, **so that** I can quickly prepare an order for checkout.
- **Acceptance Criteria**:
    1. A dedicated, full-screen UI is available at the `/pos` route, optimized for tablets.
    2. A search bar allows staff to find products by SKU or name, displaying the price and current stock level.
    3. Staff can add products to a cart and adjust quantities.
    4. The cart clearly displays each item, quantity, price, and a running total.

---
#### Story 3.2: Implement Checkout Process, Inventory Deduction, and Sales Recording
- **As a** POS Staff, **I want** to finalize the sale and have the system automatically update inventory and record the transaction, **so that** the sale is completed accurately.
- **Acceptance Criteria**:
    1. The cart has a "Checkout" button.
    2. The checkout process supports "Cash" and "Other/Manual" payment methods.
    3. Upon successful payment confirmation, the system immediately and atomically deducts the sold quantities from the WMS inventory ledger.
    4. A sales transaction record is created in the database.
    5. The interface shows a "Sale Complete" confirmation and resets for the next customer.

---
#### Story 3.3: Generate Daily Sales Summary (Z-Report)
- **As a** Store Manager, **I want** to be able to generate a sales summary at the end of the day, **so that** I can close the cash register and reconcile the day's earnings.
- **Acceptance Criteria**:
    1. A "Daily Summary" or "Z-Report" function is available.
    2. The report calculates the total sales revenue for the current day for that POS terminal.
    3. The report breaks down the total sales by payment method.
    4. The user can print the summary or view it on screen.

### Epic 4: E-commerce Integration - Lazada Channel
**Epic Goal**: To extend our business management capabilities online by deeply integrating with the Lazada platform, enabling synchronization of products, orders, and inventory data.

---
#### Story 4.1: Authorize and Connect Lazada Store
- **As a** Business Owner, **I want** to securely connect my Lazada Seller Center account to the system, **so that** the system can access my store's data.
- **Acceptance Criteria**:
    1. The system provides an admin UI for managing e-commerce connections.
    2. A "Connect Lazada Store" button redirects the user to the standard Lazada OAuth 2.0 authorization page.
    3. After the user grants permission, they are redirected back to our system.
    4. The system securely stores the access tokens required for future API calls.
    5. The UI clearly shows the connection status.

---
#### Story 4.2: Synchronize Lazada Products and Map to Product Master
- **As a** E-commerce Manager, **I want** to pull my product list from Lazada and link each item to the corresponding product in our system's Product Master, **so that** orders and inventory can be correctly identified.
- **Acceptance Criteria**:
    1. After connecting a store, the user can trigger a sync of their Lazada product listings.
    2. The system provides a UI for the user to "map" a Lazada product to an internal product SKU.
    3. The system should intelligently suggest mappings based on matching SKUs or names.
    4. A report is generated showing mapped and unmapped products.

---
#### Story 4.3: Automatically Sync New Lazada Orders and Deduct System Inventory
- **As a** E-commerce Manager, **I want** new orders from Lazada to be automatically imported and for the inventory to be immediately deducted, **so that** our central inventory is always up-to-date.
- **Acceptance Criteria**:
    1. A background worker polls the Lazada API (or uses Webhooks, if available and reliable) for new orders.
    2. Order details are saved into our "Sales Transaction Record" table.
    3. The system uses the product mapping from Story 4.2 to identify the correct internal SKUs.
    4. For each item, the stock level in our WMS inventory ledger is automatically and atomically decreased.
    5. The new Lazada order appears in the unified "Sales Orders" view.
    6. The system includes robust error handling for API failures or mapping issues.

---
#### Story 4.4: Push System Inventory Levels to Update Lazada
- **As a** E-commerce Manager, **I want** any change in my system's inventory to be automatically updated on my Lazada store, **so that** I can prevent selling out-of-stock items online.
- **Acceptance Criteria**:
    1. Whenever a product's stock level changes in our WMS, the system triggers an API call to update the stock level on Lazada.
    2. A periodic reconciliation job pushes the complete inventory state to Lazada to ensure consistency.
    3. The "Safety Stock Buffer" feature is implemented, allowing users to set a rule (e.g., when system stock is <=3, set Lazada stock to 0).

### Epic 5: Core Accounting Engine
**Epic Goal**: To build the financial brain of our ecosystem—a fully automated, real-time accounting engine that translates business transactions into accurate journal entries.

---
#### Story 5.1: Establish and Manage Chart of Accounts (COA)
- **As an** Accountant, **I want** a standard Chart of Accounts (COA) that I can customize, **so that** I can categorize all financial transactions according to my business's needs.
- **Acceptance Criteria**:
    1. The system is pre-loaded with a standard COA for a retail business.
    2. An authorized user can access a UI to view, create, edit, and deactivate accounts.
    3. Accounts with existing transactions cannot be deleted, only deactivated.

---
#### Story 5.2: Configure Sales-to-Account Mapping Rules
- **As an** Accountant, **I want** to define the rules for how different sales transactions are mapped to the COA, **so that** the system can generate correct journal entries automatically.
- **Acceptance Criteria**:
    1. A settings page exists for "Accounting Automation Rules".
    2. The user can define default accounts for common transactions (e.g., Sales Revenue, Accounts Receivable).
    3. The user can map different payment methods to specific asset accounts.

---
#### Story 5.3: Process Sales Events in Real-time and Generate Double-Entry Journal Entries
- **As a** System, **whenever** a sale is completed, **I want** to automatically generate the correct double-entry journal entry in real-time, **so that** the financial records are always up-to-date.
- **Acceptance Criteria**:
    1. The accounting module subscribes to the "Sale Completed" event from the internal message queue (Kafka).
    2. Upon receiving an event, the system uses the rules from Story 5.2 to generate a balanced double-entry journal entry within seconds.
    3. Each journal entry is stored in a `general_ledger` table and is linked to the original sales transaction record.
    4. A robust error handling mechanism (e.g., dead-letter queue) is in place to capture any events that fail to be processed.

---
#### Story 5.4: Provide General Ledger View and Traceability Function
- **As an** Accountant, **I want** to be able to view all the journal entries and trace any entry back to its original sales transaction, **so that** I can audit and verify financial data.
- **Acceptance Criteria**:
    1. A "General Ledger" UI page displays all journal entries chronologically.
    2. The user can filter the ledger by date, account, etc.
    3. Each journal entry has a direct link to view the details of the source sales transaction.

### Epic 6: Business Intelligence - Reports & Dashboards
**Epic Goal**: To bring all the data accumulated in our ecosystem to life, transforming it into intuitive, actionable business insights for managers and owners.

---
#### Story 6.1: Generate Profit & Loss Statement
- **As a** Business Owner, **I want** to be able to generate a Profit & Loss (P&L) statement for a specific period, **so that** I can understand my company's profitability.
- **Acceptance Criteria**:
    1. A "Financial Reports" page allows the user to select a date range.
    2. The system correctly calculates and displays total revenue, COGS, gross profit, expenses, and net profit for the period.
    3. The report can be viewed on screen and exported to CSV or PDF.

---
#### Story 6.2: Generate Balance Sheet
- **As an** Accountant, **I want** to be able to generate a Balance Sheet as of a specific date, **so that** I can assess the company's financial position.
- **Acceptance Criteria**:
    1. On the "Financial Reports" page, the user can select an "as of" date.
    2. The system correctly calculates the total value of Assets, Liabilities, and Equity as of that date.
    3. The report must always be balanced (Assets = Liabilities + Equity).
    4. The report can be viewed on screen and exported to CSV or PDF.

---
#### Story 6.3: Build Business Performance Dashboard
- **As a** Business Owner, **I want** a main dashboard that gives me a high-level, visual overview of my key business metrics at a glance, **so that** I can quickly understand the health of my business.
- **Acceptance Criteria**:
    1. The main login landing page is a dashboard.
    2. The dashboard displays key metric "widgets" for a selectable period (e.g., this month).
    3. Widgets must include: Total Sales Revenue, Sales by Channel (POS vs. Lazada), and Top 5 Best-Selling Products.
    4. The dashboard uses simple charts and graphs for clear visualization.