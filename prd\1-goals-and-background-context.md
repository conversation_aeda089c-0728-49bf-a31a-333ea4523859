# 1. Goals and Background Context

### Goals
- To provide a single, integrated platform for Small and Medium-sized Enterprises (SMEs) to replace their current fragmented software tools.
- To significantly reduce manual workload and human error in operations through automated data synchronization (Lazada/POS → WMS → Accounting).
- To build a powerful and self-contained ecosystem (including Accounting, POS, WMS modules) as the first version of the project for market entry.
- To lay a solid technical and product foundation for future platformization (open API, introduction of more modules) and regional expansion.

### Background Context
Currently, many SME retailers face the dilemma of "digital silos." They rely on multiple independent software to handle accounting, sales (POS), warehouse (WMS), and online sales (Lazada, etc.), preventing the free flow of data. This consumes significant manpower for manual data migration and reconciliation and severely impacts the accuracy of inventory management and business decisions due to information lag. This project aims to completely solve this pain point by providing a seamlessly integrated, all-in-one solution, helping business owners gain real-time control of their entire business and improve operational efficiency.

### Change Log
| Date       | Version | Description              | Author      |
| :--------- | :------ | :----------------------- | :---------- |
| 2025-08-04 | 1.0     | Initial Draft Creation   | John (PM)   |
| 2025-08-04 | 2.0     | Switched from MVP to Full Project scope | John (PM)   |


