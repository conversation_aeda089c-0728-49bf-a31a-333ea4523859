# Fullstack Architecture Document

## 1. Introduction
This document outlines the complete fullstack architecture for the Integrated Business Management Ecosystem, including backend systems, frontend implementation, and their integration. It will serve as the single source of technical truth. We will start from a clean slate, following community best practices (e.g., `create-next-app` for frontend, Go community standards for backend).

| Date       | Version | Description                  | Author            |
| :--------- | :------ | :--------------------------- | :---------------- |
| 2025-08-04 | 1.0     | Initial Architecture Draft   | Winston (Architect) |

## 2. High-Level Architecture
- **Technical Summary**: The system will use a modern, event-driven, containerized full-stack architecture. The frontend will be built with React (Next.js), and the backend with Go. Communication between services will be decoupled via Kafka. The entire project will be managed in a Monorepo and containerized with Docker for deployment on a major cloud platform.
- **Platform and Infrastructure**:
    - **Development Environment**: Docker Desktop on Windows 11 (including Kafka, PostgreSQL, Redis).
    - **Target Deployment Platform**: Assumed to be AWS.
    - **Core Services**: Amazon EKS (Kubernetes), Amazon RDS for PostgreSQL, Amazon ElastiCache (Redis), Amazon MSK (Kafka).
- **Repository Structure**: Monorepo.
- **Architectural and Design Patterns**: API Contract First, Event-Driven Architecture, Well-Structured Monolith (Event-Driven Monolith), Containerization.

## 3. Tech Stack
| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| **Frontend Language** | TypeScript | 5.4+ | Frontend development language | Provides strong type checking. |
| **Frontend Framework** | React (Next.js) | 14+ | Building the user interface | Powerful ecosystem, SSR capabilities. |
| **UI Component Library** | Shadcn/ui (Tailwind CSS) | Latest | UI components and styling | Highly customizable, high efficiency. |
| **State Management** | Zustand | 4+ | Frontend global state management | Lightweight and simple API. |
| **Backend Language** | Go (Golang) | 1.22+ | Backend development language | High performance and concurrency. |
| **Backend Framework** | Gin | 1.9+ | Go Web framework | Lightweight and high-performance. |
| **Database** | PostgreSQL | 16+ | Primary relational database | Powerful, ensures strong data consistency. |
| **Cache** | Redis | 7.2+ | Key-value cache | Improves system responsiveness. |
| **Message Queue** | Apache Kafka | 3.7+ | Distributed event streaming platform| Decouples services for event-driven architecture. |
| **API Style** | REST / OpenAPI | 3.0+ | Frontend-backend communication | Mature, universal, excellent tooling. |
| **Authentication** | JWT | - | User identity verification | Stateless, scalable, industry standard. |
| **Containerization** | Docker | Latest | Application packaging & isolation | Guarantees environmental consistency. |
| **Container Orchestration** | Kubernetes | 1.29+ | Production container management | Automates deployment, scaling, management. |
| **CI/CD** | GitHub Actions | - | Automated build & deployment | Seamless integration with code repository. |
| **Infrastructure as Code**| Terraform | 1.8+ | Cloud resource management | Cloud-agnostic, manages infrastructure as code. |

## 4. Data Models
This chapter defines the core business entities as TypeScript interfaces, which can be shared between the Go backend and React frontend in our Monorepo.
- **Tenant**: Represents a customer company, the root for data isolation.
- **User**: Represents an individual user belonging to a Tenant.
- **Product**: The central record for an item that is sold and stocked.
*(Other models like Sale, JournalEntry, etc. will be built upon this foundation.)*

## 5. API Specification
We will use the OpenAPI 3.0 standard. The specification will define reusable schemas, security schemes (JWT), and all paths. The "Create Product" endpoint serves as a template for all other API endpoints, defining the request body, success responses, and standard error responses.

## 6. Components
The system is broken down into logical components within an "Event-Driven Monolith".
- **Frontend Application (React)**: The user-facing UI.
- **Backend API Service (Go)**: The synchronous entry point for all frontend requests.
- **E-commerce Sync Service (Go Module)**: Handles all communication with the Lazada API.
- **Accounting Engine (Go Module)**: Listens to Kafka events to process transactions.
- **Kafka Event Bus**: The central asynchronous communication hub.

## 7. External APIs
The primary external dependency is the **Lazada Seller Center API**. The architecture accounts for its OAuth 2.0 authentication, rate limits, and key endpoints needed for syncing products, orders, and inventory. All interaction is encapsulated within the E-commerce Sync Service.

## 8. Core Workflows
Key business processes are visualized using sequence diagrams. The "Online Sale to Automated Accounting Flow" demonstrates how a sale on Lazada is captured, processed synchronously to update inventory, and then an event is published to Kafka for the Accounting Engine to process asynchronously.

## 9. Database Schema
A PostgreSQL DDL schema is provided. Key features include multi-tenancy (`tenant_id` on all business tables), foreign keys for relational integrity, and `NUMERIC` data type for financial figures to ensure precision.

## 10. Frontend Architecture
Provides concrete development patterns for the React (Next.js) application.
- **Component Architecture**: A feature-based directory structure is used.
- **State Management**: Zustand is used, with separate stores for each business domain.
- **Routing**: Next.js App Router is used, with protected routes managed via layouts or middleware.
- **Services Layer**: A centralized API client (using axios) with interceptors is defined.

## 11. Backend Architecture
Defines the internal structure of the Go "Event-Driven Monolith".
- **Service Architecture**: A modular, feature-based package structure is used.
- **Database Architecture**: The Repository Pattern is used to decouple business logic from data access.
- **Authentication Architecture**: A JWT-based authentication flow is visualized, implemented via a Gin middleware.

## 12. Unified Project Structure
A detailed ASCII tree diagram of the Monorepo structure is provided, showing the layout of `apps`, `packages`, `infrastructure`, and `docs` directories.

## 13. Development Workflow
Defines the local setup process for developers using Docker Compose.
- **Prerequisites**: Git, Node.js, Docker Desktop, Turborepo.
- **Commands**: `npm install` for setup and `npm run dev` (which runs `docker-compose up`) to start all services.
- **Environment Config**: A clear list of required `.env` variables is provided.

## 14. Deployment Architecture
- **Strategy**: Both frontend and backend are deployed as separate Docker containers to Kubernetes (EKS).
- **CI/CD Pipeline**: A conceptual `github-actions.yml` is provided, showing a multi-job workflow.
- **Environments**: A three-tier environment setup (Development, Staging, Production) is defined.

## 15. Security and Performance
- **Security**: Defines key requirements like Content Security Policy (CSP), bcrypt hashing for passwords, and strict input validation.
- **Performance**: Defines optimization strategies like code splitting, CDN for static assets, database indexing, Redis caching, and horizontal scaling via Kubernetes.

## 16. Testing Strategy
Follows the testing pyramid model, focusing on unit and integration tests.
- **Organization**: Co-location of test files for both frontend (`*.test.tsx`) and backend (`*_test.go`).
- **Examples**: Provides conceptual code snippets for a React component test, a Go API test, and an E2E test.

## 17. Coding Standards
Defines critical, non-negotiable rules for developers to ensure architectural patterns are enforced.
- **Critical Rules**: Includes mandatory type sharing in the Monorepo, use of a central API service layer, no direct access to environment variables, use of the repository pattern, and publishing events to Kafka instead of direct service calls.
- **Naming Conventions**: A table provides consistent naming conventions for frontend and backend elements.

## 18. Error Handling Strategy
A unified strategy is defined across the stack.
- **Flow**: A sequence diagram shows how an error flows from backend logic to a user-friendly notification on the frontend, with detailed logging.
- **Format**: A standard JSON error response format is defined for all APIs.
- **Implementation**: Code snippets show how a central Gin middleware on the backend and an axios interceptor on the frontend will handle errors consistently.

## 19. Monitoring and Observability
- **Stack**: Proposes a standard cloud-native stack: Prometheus for metrics, Loki/EFK for logs, Sentry/LogRocket for frontend errors, and Grafana for visualization.
- **Key Metrics**: Defines critical metrics to track for the frontend (Core Web Vitals, JS errors), and backend (RED method, database performance, Kafka consumer lag).

## 20. Checklist Results Report
An internal review using the Architect Solution Validation Checklist was performed. The architecture is deemed comprehensive and aligned with the PRD. A minor risk was noted regarding the assumption of team skill and budget constraints, which require final confirmation before project kickoff.

---
## Diagrams

### Diagram for Chapter 2: High-Level Architecture
```mermaid
graph TD
    subgraph "User Side"
        A[User Browser/Tablet]
    end

    subgraph "Cloud Platform (e.g., AWS)"
        B(React/Next.js Frontend @ EKS)
        C(API Gateway)
        D(Go Backend Service @ EKS)
        F(Kafka Message Queue @ MSK)
        E(PostgreSQL Database @ RDS)
    end

    A --> B
    B --> C
    C --> D
    D -- "Read/Write Data" --> E
    D -- "Publish/Subscribe Events" --> F
```

### Diagram for Chapter 6: Component Interaction
```mermaid
graph TD
    subgraph "User Device"
        A[Browser/Tablet]
    end

    subgraph "Our System (within the single Go application)"
        B[Frontend Application] -- "HTTP API Request" --> C[Backend API Service]
        
        C -- "Publish Event (e.g., POS Sale)" --> G[Kafka Event Bus]
        
        D[E-commerce Sync Service] -- "External API" --> E[Lazada API]
        D -- "Publish Event (e.g., Lazada Sale)" --> G
        
        F[Accounting Engine] -- "Subscribe to Events" --> G
        
        C -- "Read/Write" --> H[PostgreSQL]
        D -- "Read/Write" --> H
        F -- "Write" --> H
    end

    A --> B
```

### Diagram for Chapter 11: Authentication Architecture
```mermaid
sequenceDiagram
    participant User
    participant Frontend as React Frontend
    participant Backend as Go Backend
    participant DB as PostgreSQL

    User->>Frontend: Enters email and password
    Frontend->>Backend: POST /api/v1/auth/login
    Backend->>DB: Query user info and verify password
    DB-->>Backend: Return user information
    Backend->>Backend: Generate JWT (containing user_id, tenant_id, role)
    Backend-->>Frontend: Return JWT
    Frontend->>User: Store JWT (e.g., Secure Cookie)
```

### Diagram for Chapter 16: Testing Strategy
```plaintext 
          /        \
         /   E2E    \   (Few, covers critical flows)
        /------------\
       /              \
      /  Integration   \  (More, tests module interaction)
     /------------------\
    /    Unit Tests      \ (Many, tests individual functions)
   /----------------------\
```

### Diagram for Chapter 18: Error Handling Strategy
```mermaid
sequenceDiagram
    participant User
    participant Frontend as React Frontend
    participant Backend as Go Backend
    participant Logger as Logging Service

    User->>Frontend: 1. Performs an action (e.g., clicks Save)
    Frontend->>Backend: 2. Initiates API request
    
    Backend->>Backend: 3. Business logic fails (e.g., validation error)
    Backend->>Backend: 4. Central error middleware catches the error
    Backend-->>Frontend: 5. Returns a standardized JSON error response (e.g., 400 Bad Request)

    Frontend->>Frontend: 6. API client interceptor catches the HTTP error
    Frontend->>User: 7. Displays a user-friendly notification (e.g., "SKU already exists")
    Frontend->>Logger: 8. Sends detailed technical error to logging service