# 4. Technical Assumptions

- **Repository Structure**: A Monorepo will be used to manage the entire codebase.
- **Service Architecture**: The project will start as a Well-Structured Monolith (Event-Driven), designed for a future evolution towards a Microservices architecture.
- **Testing Requirements**: The testing strategy will focus on Unit Testing and Integration Testing.
- **Additional Assumptions**: The entire system will be designed based on container technology (Docker). The tech stack is Go for the backend, React (Next.js) for the frontend, and PostgreSQL for the database.

