# 2. Requirements

### Functional Requirements (FR)
- **FR1: User & Permission Management**: Users can register via email/password. The system supports role-based access control (e.g., Ad<PERSON>, Accountant, POS Staff).
- **FR2: Lazada Integration**: Users can securely connect their Lazada store via OAuth 2.0. The system can sync orders and products.
- **FR3: WMS Module**: The system maintains a central product master and inventory ledger, with support for manual adjustments and audit logs.
- **FR4: POS Module**: A web-based POS interface is provided for in-store sales, which deducts from the central inventory.
- **FR5: Accounting Module**: The system includes a customizable Chart of Accounts, automatically generates journal entries from sales, and can produce basic financial reports.

### Non-Functional Requirements (NFR)
- **NFR1: Performance**: API responses for typical user actions should be under 500ms. Order sync latency should not exceed 5 minutes.
- **NFR2: Security**: All sensitive user data must be encrypted at rest and in transit. The system must comply with local data protection laws (e.g., PDPA).
- **NFR3: Usability**: The initial setup for an e-commerce store should be completable by a non-technical user in under 15 minutes.
- **NFR4: Reliability**: Target uptime of 99.5%; data sync must have robust error handling, reconciliation, and retry mechanisms. Concurrency control must be in place to prevent overselling.

